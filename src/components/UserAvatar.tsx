import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface UserAvatarProps {
  userName: string;
  size?: number;
}

const UserAvatar: React.FC<UserAvatarProps> = ({ userName, size = 40 }) => {
  const initials = userName
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();

  return (
    <View
      style={[
        styles.avatar,
        { width: size, height: size, borderRadius: size / 2 },
      ]}
    >
      <Text style={[styles.text, { fontSize: size / 2 }]}>{initials}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  avatar: {
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontWeight: '700',
    color: '#333',
  },
});

export default UserAvatar;
